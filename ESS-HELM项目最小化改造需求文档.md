# ESS-HELM项目最小化改造需求文档

## 执行摘要

本文档描述了将官方element-hq/ess-helm项目（25.6.3-dev）改造为支持RouterOS本地IP检测和分离部署架构的自定义版本的最小化改造方案。

### 核心改造目标
1. **RouterOS本地IP检测**: 基于RouterOS传统API实现完全自主的公网IP获取，无需依赖任何外部服务
2. **分离部署架构**: 支持外部低配置服务器（vps）处理well-known委托和内部完整服务集群的分离部署模式
3. **官方规范兼容**: 确保与element-hq/ess-helm 25.6.3-dev版本完全兼容，遵循官方Helm Chart结构

### 用户环境特殊性说明

#### 网络环境复杂性
用户网络环境存在以下特殊情况：
- **旁路网关干扰**: 网络中存在旁路网关设备，导致标准外部IP检测服务（如ipify.org、icanhazip.com等）返回错误的IP地址
- **多层NAT结构**: 复杂的网络拓扑使得传统的HTTP-based IP检测方法不可靠
- **RouterOS核心地位**: RouterOS设备作为网络的核心路由器，是唯一能够准确获取真实公网IP的可信源

#### 分离部署架构需求
采用分离部署架构的具体原因：
- **资源优化**: 外部服务器仅需处理轻量级的well-known委托，降低成本
- **安全隔离**: 内部完整服务集群与外部访问点物理隔离，提高安全性
- **动态适应**: 支持动态IP环境下的自动配置更新和服务发现

### 技术规范合规性
- 基于官方element-hq/ess-helm 25.6.3-dev版本
- 遵循Kubernetes 1.19+和Helm 3.0+最佳实践
- 保持与官方values.schema.json的完全兼容
- 支持最新的Matrix 2.0规范和相关组件版本

## 核心改造方案

### 1. RouterOS本地IP检测系统

#### 1.1 技术实现方案
**核心原理**: 直接通过RouterOS传统API从路由器设备获取internet接口的公网IP地址，完全摒弃外部HTTP服务依赖。

**关键配置文件**: `charts/matrix-stack/values-routeros-local-ip.yaml`
```yaml
# RouterOS本地IP检测配置
routerosIpDetection:
  enabled: true
  mode: "local-only"  # 仅使用本地RouterOS API

  # RouterOS连接配置
  routeros:
    host: "${ROUTEROS_HOST}"
    username: "admin"
    password: "${ROUTEROS_PASSWORD}"
    port: 8728  # 传统API端口
    useSSL: true
    timeout: 10

  # IP检测配置
  detection:
    schedule: "*/10 * * * * *"  # 每10秒检测
    wanInterface: "ether1"      # 主WAN接口
    fallbackInterfaces:        # 备用接口
      - "pppoe-out1"
      - "lte1"

  # 验证和更新配置
  validation:
    enableLocalVerification: true   # 启用本地验证
    disableExternalServices: true   # 完全禁用外部服务

  # Cloudflare DNS更新
  cloudflare:
    enabled: true
    apiTokenSecret: "cloudflare-credentials"
    dnsRecordTtl: 60
```

#### 1.2 RouterOS API集成脚本
**文件路径**: `scripts/public-ip-detector.sh`
**核心功能**:
- RouterOS传统API连接和认证
- WAN接口IP地址获取
- 多接口故障转移支持
- 本地IP验证机制

### 2. 分离部署架构支持

#### 2.1 架构设计
**外部服务器职责**:
- 处理.well-known/matrix/server委托
- 提供轻量级反向代理

**内部服务集群职责**:
- 完整的Matrix服务栈
- Synapse、Element Web、Matrix RTC等核心服务
- 数据存储和处理

#### 2.2 部署配置文件
**文件路径**: `charts/matrix-stack/values-split-deployment.yaml`
```yaml
# 分离部署架构配置
splitDeployment:
  enabled: true

  # 外部服务器配置
  external:
    enabled: true
    namespace: "matrix-external"
    services:
      - "well-known-delegation"
      - "reverse-proxy"
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"

  # 内部服务集群配置
  internal:
    enabled: true
    namespace: "matrix-internal"
    services:
      - "synapse"
      - "element-web"
      - "matrix-rtc"
      - "postgres"

  # 动态配置更新
  dynamicConfig:
    enabled: true
    updateInterval: "30s"
    configSyncMethod: "kubernetes-api"
```

### 3. 官方规范兼容性保证

#### 3.1 Chart版本对齐
**修改文件**: `charts/matrix-stack/Chart.yaml`
```yaml
# 保持与官方最新版本同步
apiVersion: v2
name: matrix-stack
description: A Helm meta-chart for deploying a Matrix Stack from Element
type: application
version: 25.6.3-dev  # 与官方版本保持一致
dependencies: []
```

#### 3.2 Values Schema兼容
确保所有新增配置项符合官方`values.schema.json`规范：
```yaml
# 在现有schema基础上扩展
routerosIpDetection:
  type: object
  properties:
    enabled:
      type: boolean
      default: false
    mode:
      type: string
      enum: ["local-only", "hybrid"]
      default: "local-only"
```

## 关键文件清单

### 优先级1：核心功能文件
1. **`charts/matrix-stack/values-router-local-ip.yaml`** - RouterOS本地IP检测配置
2. **`scripts/routeros-local-ip-detector.sh`** - RouterOS API IP检测脚本
3. **`charts/matrix-stack/values-split-deployment.yaml`** - 分离部署架构配置
4. **`scripts/deploy-split-architecture.sh`** - 分离架构部署脚本

### 优先级2：支持和测试文件
5. **`scripts/routeros-legacy-api.py`** - RouterOS传统API Python库
6. **`scripts/test-routeros-local-ip.sh`** - RouterOS本地IP检测测试
7. **`scripts/health-check-split-deployment.sh`** - 分离部署健康检查
8. **`templates/routeros-ip-detector/`** - Kubernetes部署模板

### 优先级3：文档和示例
9. **`docs/routeros-local-ip-detection.md`** - RouterOS本地IP检测文档
10. **`docs/split-deployment-architecture.md`** - 分离部署架构文档
11. **`examples/routeros-config/`** - RouterOS配置示例
12. **`examples/split-deployment/`** - 分离部署配置示例

## 实施执行计划

### 阶段一：RouterOS本地IP检测实现（优先级：高）
**预计时间**: 2小时
**关键任务**:
1. **创建RouterOS本地IP检测配置**
   ```bash
   # 创建核心配置文件
   cat > charts/matrix-stack/values-routeros-local-ip.yaml << 'EOF'
   routerosIpDetection:
     enabled: true
     mode: "local-only"
     routeros:
       host: "${ROUTEROS_HOST}"
       username: "${ROUTEROS_USERNAME}"
       password: "${ROUTEROS_PASSWORD}"
       port: 8728
       useSSL: true
   EOF
   ```

2. **实现RouterOS API脚本**
   ```bash
   # 创建RouterOS本地IP检测脚本
   cat > scripts/routeros-local-ip-detector.sh << 'EOF'
   #!/bin/bash
   # RouterOS本地IP检测脚本 - 仅使用RouterOS API
   # 完全移除外部服务依赖

   detect_routeros_local_ip() {
       local routeros_host="$1"
       local wan_interface="$2"

       # 使用RouterOS传统API获取接口IP
       python3 scripts/routeros-legacy-api.py \
           --host "$routeros_host" \
           --interface "$wan_interface" \
           --action get-ip
   }
   EOF
   chmod +x scripts/routeros-local-ip-detector.sh
   ```

### 阶段二：分离部署架构配置（优先级：高）
**预计时间**: 1.5小时
**关键任务**:
1. **创建分离部署配置**
   ```bash
   # 创建分离部署架构配置
   cat > charts/matrix-stack/values-split-deployment.yaml << 'EOF'
   splitDeployment:
     enabled: true
     external:
       namespace: "matrix-external"
       services: ["well-known-delegation"]
     internal:
       namespace: "matrix-internal"
       services: ["synapse", "element-web", "matrix-rtc"]
   EOF
   ```

2. **实现部署自动化脚本**
   ```bash
   # 创建分离架构部署脚本
   cat > scripts/deploy-split-architecture.sh << 'EOF'
   #!/bin/bash
   # 分离部署架构自动化脚本

   deploy_external_services() {
       helm install matrix-external charts/matrix-stack/ \
           -f charts/matrix-stack/values-split-deployment.yaml \
           --set splitDeployment.external.enabled=true \
           --namespace matrix-external --create-namespace
   }

   deploy_internal_services() {
       helm install matrix-internal charts/matrix-stack/ \
           -f charts/matrix-stack/values-split-deployment.yaml \
           --set splitDeployment.internal.enabled=true \
           --namespace matrix-internal --create-namespace
   }
   EOF
   chmod +x scripts/deploy-split-architecture.sh
   ```

### 阶段三：官方规范兼容性验证（优先级：中）
**预计时间**: 1小时
**关键任务**:
1. **验证Chart兼容性**
   ```bash
   # 验证Helm Chart语法和兼容性
   helm lint charts/matrix-stack/
   helm template test charts/matrix-stack/ \
       -f charts/matrix-stack/values-routeros-local-ip.yaml \
       -f charts/matrix-stack/values-split-deployment.yaml
   ```

2. **测试RouterOS连接**
   ```bash
   # 创建RouterOS连接测试脚本
   cat > scripts/test-routeros-local-ip.sh << 'EOF'
   #!/bin/bash
   # 测试RouterOS本地IP检测功能

   test_routeros_connection() {
       echo "测试RouterOS连接..."
       ./scripts/routeros-local-ip-detector.sh
   }

   test_ip_detection() {
       echo "测试IP检测功能..."
       # 仅使用RouterOS API，不依赖外部服务
   }
   EOF
   chmod +x scripts/test-routeros-local-ip.sh
   ```

## 风险评估与缓解策略

### 高风险项及缓解措施

#### 1. RouterOS API依赖风险
**风险描述**: RouterOS设备故障或API不可用导致IP检测失败
**缓解策略**:
- 实现多RouterOS设备支持（主备模式）
- 添加RouterOS健康检查和自动故障转移
- 提供手动IP配置备用方案

#### 2. 网络环境复杂性风险
**风险描述**: 旁路网关等网络设备干扰IP检测准确性
**缓解策略**:
- 仅依赖RouterOS本地API，完全避免外部服务
- 实现多WAN接口检测和验证
- 提供网络拓扑验证工具

### 中风险项及缓解措施

#### 1. 分离部署复杂性
**风险描述**: 外部和内部服务同步配置复杂
**缓解策略**:
- 使用Kubernetes ConfigMap实现配置同步
- 实现自动化部署和配置更新脚本
- 提供详细的部署验证检查清单

#### 2. 官方版本兼容性
**风险描述**: 与element-hq/ess-helm官方更新不兼容
**缓解策略**:
- 严格遵循官方values.schema.json规范
- 实现模块化设计，便于官方版本合并
- 建立定期官方版本同步流程

## 回滚和恢复方案

### 快速回滚方案
```bash
# 1. 禁用RouterOS本地IP检测
kubectl patch configmap routeros-ip-config \
    --patch '{"data":{"enabled":"false"}}'

# 2. 切换到标准部署模式
helm upgrade matrix-stack charts/matrix-stack/ \
    --set routerosIpDetection.enabled=false \
    --set splitDeployment.enabled=false

# 3. 恢复到官方配置
git checkout official-backup
helm upgrade matrix-stack charts/matrix-stack/
```

### 渐进式回滚方案
1. **第一步**: 禁用RouterOS IP检测，使用静态IP配置
2. **第二步**: 合并分离部署到单一部署
3. **第三步**: 移除所有自定义组件，恢复官方配置

## 验证和测试方案

### RouterOS本地IP检测验证
```bash
# 1. RouterOS连接测试
./scripts/test-routeros-local-ip.sh \
    --host *********** \
    --username admin \
    --interface ether1

# 2. IP检测准确性验证
./scripts/verify-local-ip-detection.sh \
    --compare-with-manual-check \
    --validate-public-ip

# 3. 故障转移测试
./scripts/test-failover-scenarios.sh \
    --test-interface-down \
    --test-routeros-unreachable
```

### 分离部署架构验证
```bash
# 1. 外部服务验证
kubectl get pods -n matrix-external
curl -k https://external.domain.com/.well-known/matrix/server

# 2. 内部服务验证
kubectl get pods -n matrix-internal
./scripts/health-check-split-deployment.sh

# 3. 服务间通信验证
./scripts/test-external-internal-communication.sh
```

### 官方兼容性验证
```bash
# 1. Helm Chart兼容性
helm lint charts/matrix-stack/
helm template test charts/matrix-stack/ \
    -f charts/matrix-stack/values-routeros-local-ip.yaml

# 2. Kubernetes资源验证
kubectl apply --dry-run=client -f <(helm template test charts/matrix-stack/)

# 3. 官方schema验证
./scripts/validate-against-official-schema.sh
```

## 技术依赖和环境要求

### 核心系统要求
- **Kubernetes**: 1.19+ (支持最新的CRD和API版本)
- **Helm**: 3.0+ (支持Chart API v2)
- **RouterOS**: v6.45+ (传统API支持) 或 v7.1beta4+ (REST API支持)
- **Python**: 3.7+ (RouterOS API库支持)

### RouterOS特定配置要求
```bash
# RouterOS必需配置
/ip service enable api
/ip service enable api-ssl
/user group add name=api-users policy=api,read,write
/user add name=matrix-api group=api-users password=secure-password
```

### 网络环境要求
- RouterOS设备必须具有管理网络访问权限
- WAN接口必须获得真实的公网IP地址
- 内部网络与RouterOS设备之间网络连通性良好
- 如使用SSL，需要有效的证书配置

## 核心代码示例

### RouterOS本地IP检测配置
```yaml
# charts/matrix-stack/values-routeros-local-ip.yaml
routerosIpDetection:
  enabled: true
  mode: "local-only"  # 强制仅使用本地RouterOS API

  routeros:
    host: "***********"
    username: "matrix-api"
    passwordSecret:
      name: "routeros-credentials"
      key: "password"
    port: 8728  # 传统API端口
    useSSL: true
    timeout: 10

  detection:
    schedule: "*/10 * * * * *"
    wanInterface: "ether1"
    fallbackInterfaces: ["pppoe-out1", "lte1"]

  validation:
    enableLocalVerification: true
    disableExternalServices: true  # 完全禁用外部服务

  cloudflare:
    enabled: true
    apiTokenSecret:
      name: "cloudflare-credentials"
      key: "api-token"
    dnsRecordTtl: 60
```

### RouterOS API脚本示例
```bash
#!/bin/bash
# scripts/routeros-local-ip-detector.sh

detect_local_ip() {
    local routeros_host="$1"
    local wan_interface="$2"

    # 使用RouterOS传统API获取接口IP
    python3 << EOF
import socket
import struct

def routeros_api_get_ip(host, interface):
    # RouterOS传统API实现
    # 直接从路由器获取WAN接口IP
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect((host, 8728))

    # 发送API命令获取接口IP
    cmd = f"/interface/print\n=.proplist=name,address\n=?name={interface}\n"
    sock.send(cmd.encode())

    response = sock.recv(1024).decode()
    sock.close()

    # 解析响应获取IP地址
    return parse_ip_from_response(response)

print(routeros_api_get_ip("$routeros_host", "$wan_interface"))
EOF
}

# 主函数
main() {
    local detected_ip
    detected_ip=$(detect_local_ip "$ROUTEROS_HOST" "$WAN_INTERFACE")

    if [[ -n "$detected_ip" ]]; then
        echo "检测到公网IP: $detected_ip"
        update_dns_record "$detected_ip"
    else
        echo "错误: 无法从RouterOS获取IP地址"
        exit 1
    fi
}
```

### 分离部署脚本示例
```bash
#!/bin/bash
# scripts/deploy-split-architecture.sh

deploy_external_services() {
    echo "部署外部服务..."

    helm install matrix-external charts/matrix-stack/ \
        -f charts/matrix-stack/values-split-deployment.yaml \
        --set splitDeployment.external.enabled=true \
        --set splitDeployment.internal.enabled=false \
        --namespace matrix-external \
        --create-namespace

    echo "外部服务部署完成"
}

deploy_internal_services() {
    echo "部署内部服务..."

    helm install matrix-internal charts/matrix-stack/ \
        -f charts/matrix-stack/values-split-deployment.yaml \
        --set splitDeployment.external.enabled=false \
        --set splitDeployment.internal.enabled=true \
        --namespace matrix-internal \
        --create-namespace

    echo "内部服务部署完成"
}

# 主部署流程
main() {
    deploy_external_services
    deploy_internal_services

    echo "分离部署架构部署完成"
    echo "外部服务: kubectl get pods -n matrix-external"
    echo "内部服务: kubectl get pods -n matrix-internal"
}
```

## 实施总结和建议

### 改造成果预期
1. **RouterOS本地IP检测**: 实现100%本地化的IP检测方案，完全摆脱外部服务依赖
2. **分离部署架构**: 支持灵活的外部/内部服务分离，优化资源使用和安全性
3. **官方规范兼容**: 与element-hq/ess-helm 25.6.3-dev完全兼容，便于后续官方版本升级

### 关键成功因素
1. **RouterOS设备稳定性**: 确保RouterOS设备高可用性和API服务稳定
2. **网络环境验证**: 充分测试复杂网络环境下的IP检测准确性
3. **分离部署测试**: 验证外部和内部服务的正确通信和配置同步

### 实施建议
1. **分阶段实施**: 先实现RouterOS本地IP检测，再实施分离部署架构
2. **充分测试**: 在测试环境完整验证所有功能后再部署到生产环境
3. **监控和告警**: 建立完善的监控体系，及时发现和处理问题
4. **文档维护**: 保持技术文档与代码同步更新

### 预期时间投入
- **RouterOS本地IP检测实现**: 2小时
- **分离部署架构配置**: 1.5小时
- **测试和验证**: 1小时
- **文档整理**: 0.5小时
- **总计**: 5小时

### 长期维护考虑
1. **官方版本同步**: 建立定期与官方版本同步的流程
2. **RouterOS兼容性**: 跟踪RouterOS版本更新，确保API兼容性
3. **安全更新**: 定期更新相关组件和依赖库
4. **性能优化**: 持续监控和优化IP检测性能和资源使用

**重要提醒**: 本改造方案专门针对用户的特殊网络环境设计，强调RouterOS本地IP检测的自主性和分离部署架构的灵活性，确保在复杂网络环境下的可靠运行。


