#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
RouterOS 传统API动态IP检测脚本
使用端口8728/8729的二进制API协议
Copyright 2025 New Vector Ltd
SPDX-License-Identifier: AGPL-3.0-only
"""

import socket
import ssl
import hashlib
import binascii
import sys
import time
import argparse
import os
import re
from typing import Optional, Dict, List, Any

class RouterOSAPI:
    """RouterOS传统API客户端"""
    
    def __init__(self, host: str, username: str = 'admin', password: str = '', 
                 port: int = 8729, use_ssl: bool = True, timeout: int = 10):
        self.host = host
        self.username = username
        self.password = password
        self.port = port
        self.use_ssl = use_ssl
        self.timeout = timeout
        self.sock = None
        self.connected = False
        
    def connect(self) -> bool:
        """连接到RouterOS设备"""
        try:
            print(f"[INFO] 连接到RouterOS设备 {self.host}:{self.port}")
            
            # 创建socket连接
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.settimeout(self.timeout)
            
            if self.use_ssl:
                # 使用SSL连接（端口8729）
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                self.sock = context.wrap_socket(self.sock)
                
            self.sock.connect((self.host, self.port))
            
            # 执行登录
            if self._login():
                self.connected = True
                print(f"[SUCCESS] 成功连接到RouterOS设备")
                return True
            else:
                print(f"[ERROR] 登录失败")
                return False
                
        except socket.timeout:
            print(f"[ERROR] 连接超时")
            return False
        except socket.error as e:
            print(f"[ERROR] 连接失败: {e}")
            return False
        except Exception as e:
            print(f"[ERROR] 连接异常: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.sock:
            try:
                self.sock.close()
            except:
                pass
            self.sock = None
        self.connected = False
    
    def _encode_length(self, length: int) -> bytes:
        """编码长度字段"""
        if length < 0x80:
            return bytes([length])
        elif length < 0x4000:
            return bytes([length >> 8 | 0x80, length & 0xFF])
        elif length < 0x200000:
            return bytes([length >> 16 | 0xC0, length >> 8 & 0xFF, length & 0xFF])
        elif length < 0x10000000:
            return bytes([length >> 24 | 0xE0, length >> 16 & 0xFF, length >> 8 & 0xFF, length & 0xFF])
        else:
            return bytes([0xF0, length >> 24 & 0xFF, length >> 16 & 0xFF, length >> 8 & 0xFF, length & 0xFF])
    
    def _decode_length(self) -> int:
        """解码长度字段"""
        c = self.sock.recv(1)[0]
        if c & 0x80 == 0x00:
            return c
        elif c & 0xC0 == 0x80:
            return ((c & ~0xC0) << 8) + self.sock.recv(1)[0]
        elif c & 0xE0 == 0xC0:
            b = self.sock.recv(2)
            return ((c & ~0xE0) << 16) + (b[0] << 8) + b[1]
        elif c & 0xF0 == 0xE0:
            b = self.sock.recv(3)
            return ((c & ~0xF0) << 24) + (b[0] << 16) + (b[1] << 8) + b[2]
        elif c & 0xF8 == 0xF0:
            b = self.sock.recv(4)
            return (b[0] << 24) + (b[1] << 16) + (b[2] << 8) + b[3]
    
    def _send_word(self, word: str):
        """发送单词"""
        word_bytes = word.encode('utf-8')
        self.sock.send(self._encode_length(len(word_bytes)))
        self.sock.send(word_bytes)
    
    def _read_word(self) -> str:
        """读取单词"""
        length = self._decode_length()
        if length == 0:
            return ''
        return self.sock.recv(length).decode('utf-8')
    
    def _send_sentence(self, words: List[str]):
        """发送句子"""
        for word in words:
            self._send_word(word)
        self._send_word('')  # 结束标记
    
    def _read_sentence(self) -> List[str]:
        """读取句子"""
        sentence = []
        while True:
            word = self._read_word()
            if word == '':
                break
            sentence.append(word)
        return sentence
    
    def _login(self) -> bool:
        """执行登录"""
        try:
            # 发送登录命令
            self._send_sentence(['/login'])
            
            # 读取响应
            response = self._read_sentence()
            
            if not response or response[0] != '!done':
                print(f"[ERROR] 登录初始化失败")
                return False
            
            # 检查是否需要密码验证
            challenge = None
            for word in response:
                if word.startswith('=ret='):
                    challenge = word[5:]
                    break
            
            if challenge:
                # 使用挑战响应认证
                md5 = hashlib.md5()
                md5.update(b'\x00')
                md5.update(self.password.encode('utf-8'))
                md5.update(binascii.unhexlify(challenge))
                response_hash = binascii.hexlify(md5.digest()).decode('ascii')
                
                self._send_sentence(['/login', f'=name={self.username}', f'=response=00{response_hash}'])
            else:
                # 直接发送用户名密码
                self._send_sentence(['/login', f'=name={self.username}', f'=password={self.password}'])
            
            # 读取登录结果
            response = self._read_sentence()
            
            if response and response[0] == '!done':
                return True
            else:
                print(f"[ERROR] 认证失败: {response}")
                return False
                
        except Exception as e:
            print(f"[ERROR] 登录异常: {e}")
            return False
    
    def execute_command(self, command: str, arguments: Dict[str, str] = None) -> List[Dict[str, str]]:
        """执行命令"""
        if not self.connected:
            print(f"[ERROR] 未连接到设备")
            return []
        
        try:
            # 构建命令句子
            sentence = [command]
            if arguments:
                for key, value in arguments.items():
                    sentence.append(f'={key}={value}')
            
            # 发送命令
            self._send_sentence(sentence)
            
            # 读取响应
            results = []
            while True:
                response = self._read_sentence()
                if not response:
                    break
                
                if response[0] == '!done':
                    break
                elif response[0] == '!re':
                    # 解析数据记录
                    record = {}
                    for word in response[1:]:
                        if word.startswith('='):
                            parts = word[1:].split('=', 1)
                            if len(parts) == 2:
                                record[parts[0]] = parts[1]
                            else:
                                record[parts[0]] = ''
                    results.append(record)
                elif response[0] == '!trap':
                    # 错误响应
                    error_msg = "Unknown error"
                    for word in response[1:]:
                        if word.startswith('=message='):
                            error_msg = word[9:]
                            break
                    print(f"[ERROR] 命令执行失败: {error_msg}")
                    return []
            
            return results
            
        except Exception as e:
            print(f"[ERROR] 命令执行异常: {e}")
            return []

def get_wan_interfaces(api: RouterOSAPI) -> List[str]:
    """获取WAN接口列表"""
    print("[INFO] 获取网络接口列表...")
    
    interfaces = api.execute_command('/interface/print')
    if not interfaces:
        print("[ERROR] 无法获取接口列表")
        return []
    
    wan_interfaces = []
    for interface in interfaces:
        name = interface.get('name', '')
        interface_type = interface.get('type', '')
        
        # 识别可能的WAN接口
        if (name.startswith(('ether1', 'pppoe-out', 'wan', 'internet', 'wlan1')) or
            interface_type in ['ether', 'pppoe-out', 'wlan']):
            wan_interfaces.append(name)
            print(f"[INFO] 找到可能的WAN接口: {name} (类型: {interface_type})")
    
    if not wan_interfaces:
        print("[WARNING] 未找到明显的WAN接口，使用ether1作为默认")
        wan_interfaces = ['ether1']
    
    return wan_interfaces

def get_interface_ip(api: RouterOSAPI, interface_name: str) -> Optional[str]:
    """获取接口的公网IP地址"""
    print(f"[INFO] 获取接口 {interface_name} 的IP地址...")
    
    addresses = api.execute_command('/ip/address/print')
    if not addresses:
        print("[ERROR] 无法获取IP地址列表")
        return None
    
    for addr in addresses:
        if addr.get('interface') == interface_name:
            address = addr.get('address', '')
            if address:
                # 提取IP地址（去掉子网掩码）
                ip = address.split('/')[0]
                if is_public_ip(ip):
                    print(f"[SUCCESS] 接口 {interface_name} 检测到公网IP: {ip}")
                    return ip
                else:
                    print(f"[INFO] 接口 {interface_name} 的IP不是公网IP: {ip}")
    
    print(f"[WARNING] 接口 {interface_name} 未找到公网IP")
    return None

def is_public_ip(ip: str) -> bool:
    """检查IP是否为公网IP"""
    if not re.match(r'^(\d{1,3}\.){3}\d{1,3}$', ip):
        return False
    
    # 检查私有IP段
    parts = [int(x) for x in ip.split('.')]
    
    # 私有IP段
    if parts[0] == 10:
        return False
    if parts[0] == 192 and parts[1] == 168:
        return False
    if parts[0] == 172 and 16 <= parts[1] <= 31:
        return False
    if parts[0] == 127:  # 回环
        return False
    if parts[0] == 169 and parts[1] == 254:  # 链路本地
        return False
    if parts[0] >= 224:  # 多播和保留
        return False
    
    return True

def detect_public_ip(host: str, username: str = 'admin', password: str = '', 
                    port: int = 8729, use_ssl: bool = True, 
                    wan_interface: str = None) -> Optional[str]:
    """检测公网IP"""
    print(f"[INFO] 开始通过RouterOS传统API检测公网IP...")
    print(f"[INFO] 连接参数: {host}:{port} (SSL: {use_ssl})")
    
    api = RouterOSAPI(host, username, password, port, use_ssl)
    
    try:
        # 连接到设备
        if not api.connect():
            return None
        
        # 获取WAN接口
        if wan_interface:
            wan_interfaces = [wan_interface]
            print(f"[INFO] 使用指定的WAN接口: {wan_interface}")
        else:
            wan_interfaces = get_wan_interfaces(api)
        
        # 尝试从每个WAN接口获取公网IP
        for interface in wan_interfaces:
            ip = get_interface_ip(api, interface)
            if ip:
                return ip
        
        print("[ERROR] 所有WAN接口都未找到公网IP")
        return None
        
    finally:
        api.disconnect()

def main():
    parser = argparse.ArgumentParser(description='RouterOS传统API动态IP检测')
    parser.add_argument('host', help='RouterOS设备IP地址')
    parser.add_argument('--username', default='admin', help='用户名 (默认: admin)')
    parser.add_argument('--password', default='', help='密码')
    parser.add_argument('--port', type=int, default=8729, help='API端口 (默认: 8729)')
    parser.add_argument('--no-ssl', action='store_true', help='使用非SSL连接 (端口8728)')
    parser.add_argument('--interface', help='指定WAN接口名称')
    
    args = parser.parse_args()
    
    # 如果指定了no-ssl，使用端口8728
    if args.no_ssl:
        port = 8728
        use_ssl = False
    else:
        port = args.port
        use_ssl = True
    
    # 从环境变量获取密码（如果未在命令行指定）
    password = args.password or os.environ.get('ROUTEROS_PASSWORD', '')
    
    print("=" * 50)
    print("RouterOS传统API动态IP检测")
    print("=" * 50)
    
    # 检测公网IP
    public_ip = detect_public_ip(
        host=args.host,
        username=args.username,
        password=password,
        port=port,
        use_ssl=use_ssl,
        wan_interface=args.interface
    )
    
    if public_ip:
        print(f"\n[SUCCESS] 检测到公网IP: {public_ip}")
        print(f"可以使用以下配置:")
        print(f"export ROUTEROS_HOST=\"{args.host}\"")
        print(f"export ROUTEROS_USERNAME=\"{args.username}\"")
        print(f"export ROUTEROS_PASSWORD=\"{password}\"")
        print(f"export ROUTEROS_PORT=\"{port}\"")
        print(f"export ROUTEROS_USE_SSL=\"{use_ssl}\"")
        sys.exit(0)
    else:
        print(f"\n[ERROR] 无法检测到公网IP")
        sys.exit(1)

if __name__ == '__main__':
    main()
