# 最小化改造需求文档

## 📋 概述

本文档提供了将当前项目从25.6.2-dev升级到25.6.3-dev的精确改造计划，确保保持所有自定义功能的完整性。

## 🎯 改造目标

1. **版本同步**：升级到官方25.6.3-dev版本
2. **功能保持**：保留所有RouterOS API动态IP检测功能
3. **兼容性确保**：确保分离部署架构正常工作
4. **风险最小化**：采用最小化改造策略，减少破坏性变更

## 📊 改造范围评估

### 需要修改的文件
- `charts/matrix-stack/Chart.yaml` (版本号更新)
- `CHANGELOG.md` (变更日志同步)

### 需要保留的自定义文件
- 所有RouterOS相关脚本和文档
- 所有自定义部署脚本
- 所有用户配置文件
- 所有自定义模板文件

### 需要验证的功能
- RouterOS API动态IP检测系统
- 分离部署架构
- 自定义运维工具

## 🔧 详细改造步骤

### 步骤1：备份当前项目

```bash
# 1.1 创建完整备份
cd /Volumes/ext_hd/AI/Augment/02.20-06.19-2025/
cp -r ess-helm ess-helm-backup-$(date +%Y%m%d-%H%M%S)

# 1.2 创建Git提交点（如果使用Git）
cd ess-helm
git add .
git commit -m "备份：升级到25.6.3-dev之前的状态"
git tag "pre-25.6.3-upgrade-$(date +%Y%m%d-%H%M%S)"
```

### 步骤2：精确文件修改

#### 2.1 更新Chart.yaml

**文件路径**: `charts/matrix-stack/Chart.yaml`

**当前内容** (第9行):
```yaml
version: 25.6.2-dev
```

**修改为**:
```yaml
version: 25.6.3-dev
```

**精确修改命令**:
```bash
cd /Volumes/ext_hd/AI/Augment/02.20-06.19-2025/ess-helm
sed -i '' 's/version: 25.6.2-dev/version: 25.6.3-dev/' charts/matrix-stack/Chart.yaml
```

**验证修改**:
```bash
grep "version:" charts/matrix-stack/Chart.yaml
# 预期输出: version: 25.6.3-dev
```

#### 2.2 更新CHANGELOG.md

**文件路径**: `CHANGELOG.md`

**需要添加的内容** (在文件开头的版本历史部分):

```markdown
# ESS Community Helm Chart 25.6.3-dev (升级版本)

### 升级说明

- 从官方element-hq/ess-helm项目同步到25.6.3-dev版本
- 保留所有RouterOS API动态IP检测功能
- 保留所有分离部署架构功能
- 保留所有自定义运维工具

### 官方25.6.3版本更新内容

#### Fixed
- matrix-tools: Skip any completed pods when scaling down synapse pods in syn2mas migration. (#546)
- Fix Matrix RTC's SFU constructing an invalid Service if given too wide a nodePort range. (#549)
- Fix comments around the image tag and digest in the values file. (#553)
- Fix certificate name inconsistencies between setup docs and values file fragments. (#555)
- Fix MatrixRTC RTCSession Error if a `push-rules` Synapse worker is enabled. (#557)
- Fix `extraEnv` with duplicate keys not being correctly merged. (#559)
- Document the need for removal of generated secrets & deployment marker configmap when uninstalling. (#567)

#### Changed
- Omit the UDP port range metadata for Matrix RTC's SFU if the range is larger than 100 ports. (#549)
- Remove warning about deprecated `prometheus_port` config value in Matrix RTC SFU. (#550)
- Upgrade Matrix RTC SFU to v1.9.0. (#552)
- Document `extraEnv` in `values.yaml` for every workload. (#559)
- Consistently handle user provided `extraEnv` versus chart configured `env`. (#559)
- Upgrade Matrix Authentication Service to v0.17.1. (#564)
- Upgrade Element Web to v1.11.104. (#565)

---

```

**精确修改命令**:
```bash
# 创建临时文件包含新内容
cat > /tmp/changelog_header.md << 'EOF'
<!--
Copyright 2025 New Vector Ltd

SPDX-License-Identifier: AGPL-3.0-only
-->

<!-- towncrier release notes start -->

# ESS Community Helm Chart 25.6.3-dev (升级版本)

### 升级说明

- 从官方element-hq/ess-helm项目同步到25.6.3-dev版本
- 保留所有RouterOS API动态IP检测功能
- 保留所有分离部署架构功能
- 保留所有自定义运维工具

### 官方25.6.3版本更新内容

#### Fixed
- matrix-tools: Skip any completed pods when scaling down synapse pods in syn2mas migration. (#546)
- Fix Matrix RTC's SFU constructing an invalid Service if given too wide a nodePort range. (#549)
- Fix comments around the image tag and digest in the values file. (#553)
- Fix certificate name inconsistencies between setup docs and values file fragments. (#555)
- Fix MatrixRTC RTCSession Error if a `push-rules` Synapse worker is enabled. (#557)
- Fix `extraEnv` with duplicate keys not being correctly merged. (#559)
- Document the need for removal of generated secrets & deployment marker configmap when uninstalling. (#567)

#### Changed
- Omit the UDP port range metadata for Matrix RTC's SFU if the range is larger than 100 ports. (#549)
- Remove warning about deprecated `prometheus_port` config value in Matrix RTC SFU. (#550)
- Upgrade Matrix RTC SFU to v1.9.0. (#552)
- Document `extraEnv` in `values.yaml` for every workload. (#559)
- Consistently handle user provided `extraEnv` versus chart configured `env`. (#559)
- Upgrade Matrix Authentication Service to v0.17.1. (#564)
- Upgrade Element Web to v1.11.104. (#565)

---

EOF

# 备份原始CHANGELOG
cp CHANGELOG.md CHANGELOG.md.backup

# 合并新内容
cat /tmp/changelog_header.md > CHANGELOG.md.new
tail -n +8 CHANGELOG.md.backup >> CHANGELOG.md.new
mv CHANGELOG.md.new CHANGELOG.md

# 清理临时文件
rm /tmp/changelog_header.md
```

### 步骤3：验证改造结果

#### 3.1 验证版本更新
```bash
# 检查Chart版本
grep "version:" charts/matrix-stack/Chart.yaml

# 检查CHANGELOG更新
head -20 CHANGELOG.md | grep "25.6.3-dev"
```

#### 3.2 验证自定义功能完整性
```bash
# 检查RouterOS脚本存在
ls -la scripts/routeros-*.sh scripts/dynamic-ip-manager.sh

# 检查自定义配置存在
ls -la charts/matrix-stack/user_values/
ls -la scripts/*config*.yaml

# 检查自定义文档存在
ls -la RouterOS-*.md 动态IP*.md
```

#### 3.3 验证Helm Chart语法
```bash
# 验证Chart语法正确性
helm lint charts/matrix-stack/

# 验证模板渲染
helm template test charts/matrix-stack/ --dry-run
```

### 步骤4：功能测试验证

#### 4.1 RouterOS API功能测试
```bash
# 测试RouterOS连接脚本
./scripts/routeros-troubleshoot.sh --help

# 测试动态IP管理脚本
./scripts/dynamic-ip-manager.sh --help

# 验证配置文件格式
yamllint scripts/routeros-config-example.yaml
```

#### 4.2 部署脚本测试
```bash
# 测试分离部署脚本
./scripts/deploy-split-architecture.sh --help

# 测试内部服务器部署脚本
./scripts/deploy-internal-server.sh --help

# 测试健康检查脚本
./scripts/health-check-internal.sh --help
```

## ⚠️ 风险评估和注意事项

### 低风险项目
1. **Chart版本更新**: 仅数字变更，无功能影响
2. **CHANGELOG更新**: 纯文档变更，无功能影响

### 中等风险项目
1. **Matrix RTC SFU变更**: 可能影响分离部署中的RTC配置
2. **extraEnv处理变更**: 可能影响自定义环境变量配置

### 需要特别关注的项目
1. **Matrix Authentication Service v0.17.1**: 配置格式可能有变化
2. **Element Web v1.11.104**: 前端兼容性需要验证

### 风险缓解措施
1. **完整备份**: 升级前创建完整项目备份
2. **分步验证**: 每个步骤后立即验证功能
3. **回滚准备**: 准备快速回滚方案
4. **测试环境**: 建议先在测试环境执行改造

## 🔄 回滚方案

### 快速回滚步骤

如果改造后发现问题，可以快速回滚：

```bash
# 方案1：从备份恢复
cd /Volumes/ext_hd/AI/Augment/02.20-06.19-2025/
rm -rf ess-helm
cp -r ess-helm-backup-* ess-helm

# 方案2：Git回滚（如果使用Git）
cd ess-helm
git reset --hard pre-25.6.3-upgrade-*

# 方案3：手动回滚关键文件
cd ess-helm
echo "version: 25.6.2-dev" > charts/matrix-stack/Chart.yaml
cp CHANGELOG.md.backup CHANGELOG.md
```

### 回滚验证
```bash
# 验证版本回滚
grep "version:" charts/matrix-stack/Chart.yaml
# 预期输出: version: 25.6.2-dev

# 验证功能正常
./scripts/dynamic-ip-manager.sh --help
helm lint charts/matrix-stack/
```

## ✅ 改造完成检查清单

- [ ] 备份原始项目
- [ ] 更新Chart.yaml版本号
- [ ] 更新CHANGELOG.md
- [ ] 验证Helm Chart语法
- [ ] 测试RouterOS API功能
- [ ] 测试分离部署功能
- [ ] 验证所有自定义脚本
- [ ] 检查所有自定义配置文件
- [ ] 运行完整功能测试
- [ ] 创建升级后的Git标签

## 📞 技术支持

如果在改造过程中遇到问题：

1. **检查日志**: 查看详细的错误信息
2. **对比差异**: 使用diff工具对比文件变化
3. **分步回滚**: 逐步回滚到上一个工作状态
4. **功能验证**: 重新验证每个自定义功能

改造预计耗时：**30-60分钟**
风险等级：**低到中等**
建议执行时间：**非生产高峰期**
