# RouterOS 传统API动态IP检测使用指南

## 📋 概述

由于您的RouterOS设备只开放了传统API端口（8728/8729），而没有REST API端口（80/443），我为您创建了基于传统API的解决方案。

## 🔍 当前情况

根据您的测试结果：
- ❌ 端口443（HTTPS REST API）不可达
- ❌ 端口80（HTTP REST API）不可达  
- ✅ 端口8728（传统API Binary）开放
- ✅ 端口8729（传统API-SSL Binary）开放

## 🛠️ 解决方案

### 方案1：使用传统API脚本（推荐）

我已经为您创建了支持传统API的脚本：

#### 1.1 Python版本（功能完整）

```bash
# 基本使用
./scripts/routeros-legacy-api.py 您的RouterOS_IP

# 指定用户名和密码
./scripts/routeros-legacy-api.py 您的RouterOS_IP --username admin --password 您的密码

# 使用非SSL连接（端口8728）
./scripts/routeros-legacy-api.py 您的RouterOS_IP --username admin --password 您的密码 --no-ssl

# 指定WAN接口
./scripts/routeros-legacy-api.py 您的RouterOS_IP --username admin --password 您的密码 --interface ether1
```

#### 1.2 测试脚本（检查连接）

```bash
# 快速测试
./scripts/routeros-legacy-test.sh 您的RouterOS_IP

# 完整测试
./scripts/routeros-legacy-test.sh 您的RouterOS_IP admin 您的密码

# 使用非SSL
./scripts/routeros-legacy-test.sh 您的RouterOS_IP admin 您的密码 --no-ssl
```

### 方案2：启用REST API服务（最简单）

如果您可以访问RouterOS设备管理界面：

#### 2.1 通过Winbox启用

1. **打开Winbox**，连接到您的设备（端口8291）
2. **导航到** `IP` → `Services`
3. **启用** `www-ssl` 服务
4. **设置端口** 为 `443`
5. **应用配置**

#### 2.2 通过WebFig启用

1. 访问 `http://您的RouterOS_IP/webfig`
2. 导航到 `IP` → `Services`
3. 启用 `www-ssl` 服务

#### 2.3 通过CLI启用

如果您有SSH/Telnet访问：
```bash
/ip service enable www-ssl
/ip service set www-ssl port=443
```

## 🚀 立即测试

### 测试传统API连接

```bash
# 运行测试脚本
./scripts/routeros-legacy-test.sh 您的RouterOS_IP admin 您的密码

# 如果成功，运行IP检测
./scripts/routeros-legacy-api.py 您的RouterOS_IP --username admin --password 您的密码
```

### 预期输出

成功的话您会看到：
```
[SUCCESS] 检测到公网IP: xxx.xxx.xxx.xxx
可以使用以下配置:
export ROUTEROS_HOST="您的IP"
export ROUTEROS_USERNAME="admin"
export ROUTEROS_PASSWORD="您的密码"
export ROUTEROS_PORT="8729"
export ROUTEROS_USE_SSL="true"
```

## 🔧 集成到现有系统

### 选项1：修改现有脚本

如果您想继续使用bash脚本，我可以修改 `dynamic-ip-manager.sh` 来调用Python版本：

```bash
# 在detect_public_ip()函数中添加
if [[ -f "./scripts/routeros-legacy-api.py" ]]; then
    python3 ./scripts/routeros-legacy-api.py "$ROUTEROS_HOST" \
        --username "$ROUTEROS_USERNAME" \
        --password "$ROUTEROS_PASSWORD"
fi
```

### 选项2：创建包装脚本

创建一个包装脚本来桥接两种API：

```bash
#!/bin/bash
# 检测可用的API类型并调用相应的脚本
if curl -s --max-time 3 "https://$ROUTEROS_HOST:443/rest/system/identity" >/dev/null 2>&1; then
    # 使用REST API
    ./scripts/dynamic-ip-manager.sh "$@"
else
    # 使用传统API
    python3 ./scripts/routeros-legacy-api.py "$ROUTEROS_HOST" "$@"
fi
```

## 📋 系统要求

### 传统API方案要求

1. **Python环境**：
   - Python 3.6+
   - 标准库（无需额外安装包）

2. **网络连接**：
   - 能够访问RouterOS设备的8728或8729端口

3. **RouterOS版本**：
   - 所有版本都支持传统API

### 安装Python（如果需要）

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3

# CentOS/RHEL
sudo yum install python3

# macOS
brew install python3

# 验证安装
python3 --version
```

## 🔍 故障排除

### 常见问题

#### 1. Python未安装
```
[WARNING] 未检测到Python环境
```
**解决**：安装Python3

#### 2. 连接被拒绝
```
[ERROR] 连接失败: Connection refused
```
**解决**：检查端口8728/8729是否开放

#### 3. 认证失败
```
[ERROR] 认证失败
```
**解决**：检查用户名密码，尝试空密码

#### 4. 无公网IP
```
[WARNING] 接口 ether1 未找到公网IP
```
**解决**：检查WAN接口配置，或指定正确的接口名称

### 调试模式

```bash
# 启用详细输出
python3 ./scripts/routeros-legacy-api.py 您的IP --username admin --password 您的密码 -v
```

## 💡 建议

### 短期解决方案
使用传统API脚本，立即可用：
```bash
./scripts/routeros-legacy-test.sh 您的RouterOS_IP admin 您的密码
```

### 长期解决方案
启用REST API服务，使用更现代的实现：
1. 通过Winbox启用www-ssl服务
2. 使用原有的REST API脚本
3. 享受更好的性能和调试体验

## 📞 获取帮助

如果遇到问题：

1. **运行测试脚本**：
   ```bash
   ./scripts/routeros-legacy-test.sh 您的IP admin 您的密码
   ```

2. **检查Python环境**：
   ```bash
   python3 --version
   ```

3. **验证网络连接**：
   ```bash
   telnet 您的IP 8729
   ```

4. **查看详细错误**：
   ```bash
   python3 ./scripts/routeros-legacy-api.py 您的IP --username admin --password 您的密码 2>&1 | tee debug.log
   ```

现在您可以立即测试传统API方案了！
